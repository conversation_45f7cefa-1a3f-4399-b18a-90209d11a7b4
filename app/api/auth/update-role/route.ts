import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth/auth";
import { db } from "@/lib/db";
import { users } from "@/lib/db/schema";
import { eq } from "drizzle-orm";
import { z } from "zod";

const updateRoleSchema = z.object({
	userId: z.string(),
	role: z.enum(["adopter", "rescuer", "clinic"]),
});

export async function POST(request: NextRequest) {
	try {
		// Parse request body
		const body = await request.json();
		const { userId, role } = updateRoleSchema.parse(body);

		// Convert userId to number for database query
		const userIdNumber = parseInt(userId);
		if (isNaN(userIdNumber)) {
			return NextResponse.json(
				{ error: "Invalid user ID" },
				{ status: 400 }
			);
		}

		// Check if user exists
		const user = await db.query.users.findFirst({
			where: eq(users.id, userIdNumber),
		});

		if (!user) {
			return NextResponse.json(
				{ error: "User not found" },
				{ status: 404 }
			);
		}

		// Update user role
		const [updatedUser] = await db
			.update(users)
			.set({
				role,
				updatedAt: new Date(),
			})
			.where(eq(users.id, userIdNumber))
			.returning();

		return NextResponse.json({
			success: true,
			user: {
				id: updatedUser.id,
				role: updatedUser.role,
			},
		});
	} catch (error) {
		console.error("Error updating user role:", error);
		
		if (error instanceof z.ZodError) {
			return NextResponse.json(
				{ error: "Invalid request data", details: error.errors },
				{ status: 400 }
			);
		}

		return NextResponse.json(
			{ error: "Internal server error" },
			{ status: 500 }
		);
	}
}
