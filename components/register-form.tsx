"use client";

import { useState } from "react";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Button } from "@/components/ui/button";
import {
	Form,
	FormControl,
	FormDescription,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import { signUp } from "@/lib/auth/client";
import { useTranslations } from "next-intl";
import { useRouter as useIntlRouter } from "@/lib/i18n/navigation";

const registerSchema = z.object({
	name: z.string().min(2, {
		message: "Name must be at least 2 characters.",
	}),
	email: z.email({
		message: "Please enter a valid email address.",
	}),
	password: z.string().min(8, {
		message: "Password must be at least 8 characters.",
	}),
	role: z.enum(["adopter", "rescuer", "clinic"], {
		message: "Please select a role.",
	}),
});

type RegisterValues = z.infer<typeof registerSchema>;

export function RegisterForm({ initialRole }: { initialRole?: string }) {
	const [isSubmitting, setIsSubmitting] = useState(false);
	const intlRouter = useIntlRouter();
	const { toast } = useToast();
	const t = useTranslations("forms.register");

	const form = useForm<RegisterValues>({
		resolver: zodResolver(registerSchema),
		defaultValues: {
			name: "",
			email: "",
			password: "",
			role:
				(initialRole as "adopter" | "rescuer" | "clinic") || "adopter",
		},
	});

	async function onSubmit(values: RegisterValues) {
		setIsSubmitting(true);

		try {
			// Slug will be automatically generated by the database hook
			const { error, data } = await signUp.email({
				name: values.name,
				email: values.email,
				password: values.password,
				callbackURL: `/auth/login`,
			});

			if (error) {
				toast({
					title: t("errorTitle"),
					description: error.message || t("errorMessage"),
					variant: "destructive",
				});
			} else {
				// If role is not the default "adopter", update it via API
				if (values.role !== "adopter" && data?.user?.id) {
					try {
						const response = await fetch("/api/auth/update-role", {
							method: "POST",
							headers: {
								"Content-Type": "application/json",
							},
							body: JSON.stringify({
								userId: data.user.id,
								role: values.role,
							}),
						});

						if (!response.ok) {
							console.warn(
								"Failed to update user role:",
								await response.text()
							);
							// Don't show error to user as registration was successful
						}
					} catch (roleError) {
						console.warn("Failed to update user role:", roleError);
						// Don't show error to user as registration was successful
					}
				}

				toast({
					title: t("successTitle"),
					description: t("successMessage"),
				});
				intlRouter.push("/auth/login");
			}
		} catch (error) {
			toast({
				title: t("errorTitle"),
				description: t("tryAgainMessage"),
				variant: "destructive",
			});
		} finally {
			setIsSubmitting(false);
		}
	}

	return (
		<Form {...form}>
			<form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
				<FormField
					control={form.control}
					name="name"
					render={({ field }) => (
						<FormItem>
							<FormLabel>{t("name")}</FormLabel>
							<FormControl>
								<Input
									placeholder={t("namePlaceholder")}
									{...field}
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>

				<FormField
					control={form.control}
					name="email"
					render={({ field }) => (
						<FormItem>
							<FormLabel>{t("email")}</FormLabel>
							<FormControl>
								<Input
									type="email"
									placeholder="<EMAIL>"
									{...field}
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>

				<FormField
					control={form.control}
					name="password"
					render={({ field }) => (
						<FormItem>
							<FormLabel>{t("password")}</FormLabel>
							<FormControl>
								<Input
									type="password"
									placeholder="********"
									{...field}
								/>
							</FormControl>
							<FormDescription>
								{t("passwordRequirement")}
							</FormDescription>
							<FormMessage />
						</FormItem>
					)}
				/>

				<FormField
					control={form.control}
					name="role"
					render={({ field }) => (
						<FormItem>
							<FormLabel>{t("roleLabel")}</FormLabel>
							<Select
								onValueChange={field.onChange}
								defaultValue={field.value}
							>
								<FormControl>
									<SelectTrigger>
										<SelectValue
											placeholder={t("rolePlaceholder")}
										/>
									</SelectTrigger>
								</FormControl>
								<SelectContent>
									<SelectItem value="adopter">
										{t("roleAdopter")}
									</SelectItem>
									<SelectItem value="rescuer">
										{t("roleRescuer")}
									</SelectItem>
									<SelectItem value="clinic">
										{t("roleClinic")}
									</SelectItem>
								</SelectContent>
							</Select>
							<FormDescription>
								{t("roleDescription")}
							</FormDescription>
							<FormMessage />
						</FormItem>
					)}
				/>

				<Button
					type="submit"
					className="w-full"
					disabled={isSubmitting}
				>
					{isSubmitting ? t("submitting") : t("submit")}
				</Button>
			</form>
		</Form>
	);
}
