import { betterAuth } from "better-auth";
import { drizzleAdapter } from "better-auth/adapters/drizzle";
import { admin } from "better-auth/plugins";
import { createAuthMiddleware } from "better-auth/api";
import { db, schema } from "../db";
import { Resend } from "resend";
import { SignupVerification } from "@/components/emails/signup-verification";
import { env } from "../env";
import { generateUniqueUserSlug } from "../utils/slug";

const resend = new Resend(env.RESEND_API_KEY);

export const auth = betterAuth({
	database: drizzleAdapter(db, {
		provider: "pg",
		usePlural: true,
		schema,
	}),
	user: {
		additionalFields: {
			slug: {
				type: "string",
				required: true,
				input: false, // Don't allow user to set slug directly
			},
			role: {
				type: "string",
				required: true,
				input: false, // Don't allow user to set role directly
			},
		},
	},
	emailAndPassword: {
		enabled: true,
		requireEmailVerification: true,
	},
	hooks: {
		before: createAuthMiddleware(async (ctx) => {
			if (ctx.path === "/sign-up/email") {
				const slug = await generateUniqueUserSlug(ctx.body.name);
				return {
					context: {
						...ctx,
						body: {
							...ctx.body,
							slug,
						},
					},
				};
			}
		}),
	},
	emailVerification: {
		sendOnSignUp: true,
		sendVerificationEmail: async ({ user, url }, request) => {
			const { error } = await resend.emails.send({
				from: "Adoption <<EMAIL>>",
				to: [user.email],
				subject: "Email Verification",
				react: SignupVerification({
					verificationUrl: url,
					name: user.name,
				}) as React.ReactNode,
			});
			if (error) {
				console.log("verification email error start--------------");
				console.error(error);
				console.log("verification email error end--------------");
			}

			// TODO: Find a reliable email service for production
			if (process.env.NODE_ENV === "development") {
				console.log("Development mode - Verification URL:", url);
			}
		},
	},
	databaseHooks: {
		user: {
			create: {
				before: async (user) => {
					const slug = await generateUniqueUserSlug(user.name);
					const result = {
						data: {
							...user,
							slug,
						},
					};
					return result;
				},
				after: async (user) => {
					console.log(user);
				},
			},
		},
	},
	plugins: [
		admin({
			defaultRole: "adopter",
			adminRoles: ["admin"],
		}),
	],
	advanced: {
		database: {
			generateId: false,
		},
	},
});
